import { z } from "zod";

export const SHIPPING_METHODS = [
  {
    id: "standard",
    name: "Standard Shipping",
    price: 50,
    estimatedDays: "3-5 วันทำการ"
  },
  {
    id: "express",
    name: "Express Shipping", 
    price: 100,
    estimatedDays: "1-2 วันทำการ"
  }
];

export const PAYMENT_METHODS = [
  {
    id: "credit_card",
    type: "credit_card" as const,
    name: "Credit or Debit Card"
  },
  {
    id: "truemoney",
    type: "truemoney" as const,
    name: "<PERSON><PERSON><PERSON>"
  },
  {
    id: "alipay",
    type: "alipay" as const,
    name: "Ali<PERSON><PERSON>"
  },
  {
    id: "thai_qr",
    type: "thai_qr" as const,
    name: "Thai QR Payment"
  },
  {
    id: "mobile_banking",
    type: "mobile_banking" as const,
    name: "Mobile Banking"
  }
];

export const MOBILE_BANKING_OPTIONS = [
  "BualuangmBanking",
  "K PLUS", 
  "KTB NEXT",
  "KMA",
  "SCB Easy"
];

export const checkoutSchema = z.object({
  contactInfo: z.object({
    email: z.string().email("กรุณาป้อนอีเมลที่ถูกต้อง")
  }),
  shippingAddress: z.object({
    firstName: z.string().min(1, ""),
    lastName: z.string().min(1, ""),
    company: z.string().optional(),
    address_line1: z.string().min(1, "กรุณาป้อนที่อยู่"),
    address_line2: z.string().optional(),
    subdistrict: z.string().min(1, "กรุณาป้อนตำบล"),
    district: z.string().min(1, "กรุณาป้อนอำเภอ"),
    city: z.string().min(1, "กรุณาป้อนเมือง"),
    postal_code: z.string().min(5, "กรุณาป้อนรหัสไปรษณีย์"),
    province: z.string().min(1, "กรุณาเลือกจังหวัด"),
    country: z.string().min(1, "กรุณาเลือกประเทศ")
  }),
  taxInvoice: z.object({
    wantTaxInvoice: z.boolean(),
    personalInfo: z.object({
      type: z.enum(["individual", "company"]),
      firstName: z.string().min(1, "โปรดป้อนชื่อของคุณสำหรับการชำระภาษีศุลกากร"),
      lastName: z.string().min(1, "กรุณาป้อนนามสกุล"),
      email: z.string().email("กรุณาป้อนอีเมลที่ถูกต้อง"),
      phone: z.string().min(10, "กรุณาป้อนเบอร์โทรศัพท์")
    }).optional(),
    taxInfo: z.object({
      taxId: z.string().min(1, "กรุณาป้อนหมายเลขประจำตัวผู้เสียภาษี"),
      address_line1: z.string().min(1, "กรุณาป้อนที่อยู่สำหรับออกใบกำกับภาษี"),
      address_line2: z.string().optional(),
      subdistrict: z.string().min(1, "กรุณาป้อนตำบล"),
      district: z.string().min(1, "กรุณาป้อนอำเภอ"),
      city: z.string().min(1, "กรุณาป้อนเมือง"),
      postal_code: z.string().min(5, "กรุณาป้อนรหัสไปรษณีย์"),
      province: z.string().min(1, "กรุณาเลือกจังหวัด"),
      country: z.string().min(1, "กรุณาเลือกประเทศ"),
      useSameAddress: z.boolean()
    }).optional()
  }),
  shippingMethod: z.string().min(1, "กรุณาเลือกวิธีการจัดส่ง"),
  paymentMethod: z.string().min(1, "กรุณาเลือกวิธีการชำระเงิน"),
  agreeToTerms: z.boolean().refine(val => val === true, {
    message: "กรุณายอมรับข้อกำหนดในการให้บริการ"
  })
});

export type CheckoutFormData = z.infer<typeof checkoutSchema>;
