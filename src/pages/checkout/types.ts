export interface ContactInfo {
  email: string;
}

export interface ShippingAddress {
  company?: string;
  address: string;
  apartment?: string;
  city: string;
  postalCode: string;
  province: string;
  country: string;
}

export interface TaxInvoiceInfo {
  wantTaxInvoice: boolean;
  personalInfo?: {
    type: 'individual' | 'company';
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  taxInfo?: {
    taxId: string;
    address: string;
    apartment?: string;
    city: string;
    postalCode: string;
    province: string;
    useSameAddress: boolean;
  };
}

export interface ShippingMethod {
  id: string;
  name: string;
  price: number;
  estimatedDays: string;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'truemoney' | 'alipay' | 'thai_qr' | 'mobile_banking';
  name: string;
}

export interface CheckoutFormData {
  contactInfo: ContactInfo;
  shippingAddress: ShippingAddress;
  taxInvoice: TaxInvoiceInfo;
  shippingMethod: string;
  paymentMethod: string;
  agreeToTerms: boolean;
}

export interface CartItem {
  id: number;
  product_variant: {
    id: number;
    sku: string;
    price: number;
    stock_quantity: number;
    option_values: Array<{
      option: string;
      value: string;
    }>;
    first_image_url: string;
    product_name: string;
  };
  quantity: number;
}

export interface CartSummary {
  subtotal: number;
  shipping: number;
  discount: number;
  total: number;
}
