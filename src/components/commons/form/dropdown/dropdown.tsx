import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@base/select";
import { Label } from "@base/label";

export interface DropdownOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface DropdownProps {
  id?: string;
  label?: string;
  placeholder?: string;
  value?: string;
  options: DropdownOption[];
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  onValueChange?: (value: string) => void;
}

export const Dropdown: React.FC<DropdownProps> = ({
  id,
  label,
  placeholder = "เลือก...",
  value,
  options,
  error,
  disabled = false,
  required = false,
  className = "",
  onValueChange,
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor={id} className="block text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <Select
        value={value}
        onValueChange={onValueChange}
        disabled={disabled}
      >
        <SelectTrigger id={id} className={error ? "border-red-500" : ""}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {error && (
        <p className="text-xs text-red-600 mt-1">
          {error}
        </p>
      )}
    </div>
  );
};
