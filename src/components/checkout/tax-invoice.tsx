import React, { useEffect } from "react";
import {
  Controller,
  type Control,
  type FieldErrors,
  type UseFormWatch,
  type UseFormSetValue,
} from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@base/card";
import { InputField } from "@/components/commons/form/input-field";
import { Dropdown } from "@/components/commons/form/dropdown";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@base/select";
import { RadioGroup, RadioGroupItem } from "@base/radio-group";
import { Checkbox } from "@base/checkbox";
import { Label } from "@base/label";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/plain.css";
import type { CheckoutFormData } from "@/pages/checkout/constants";

interface TaxInvoiceProps {
  control: Control<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  watch: UseFormWatch<CheckoutFormData>;
  setValue: UseFormSetValue<CheckoutFormData>;
}

export const TaxInvoice: React.FC<TaxInvoiceProps> = ({
  control,
  errors,
  watch,
  setValue,
}) => {
  // Helper function to safely get error message
  const getErrorMessage = (error: any): string | undefined => {
    return error?.message || undefined;
  };
  const watchTaxInvoice = watch("taxInvoice.wantTaxInvoice");
  const watchUseSameAddress = watch("taxInvoice.taxInfo.useSameAddress");

  const copyShippingToTax = () => {
    const shippingAddress = watch("shippingAddress");
    setValue("taxInvoice.taxInfo.address_line1", shippingAddress.address_line1);
    setValue("taxInvoice.taxInfo.address_line2", shippingAddress.address_line2);
    setValue("taxInvoice.taxInfo.subdistrict", shippingAddress.subdistrict);
    setValue("taxInvoice.taxInfo.district", shippingAddress.district);
    setValue("taxInvoice.taxInfo.city", shippingAddress.city);
    setValue("taxInvoice.taxInfo.province", shippingAddress.province);
    setValue("taxInvoice.taxInfo.postal_code", shippingAddress.postal_code);
    setValue("taxInvoice.taxInfo.country", shippingAddress.country);
  };

  useEffect(() => {
    if (watchUseSameAddress) {
      copyShippingToTax();
    }
  }, [watchUseSameAddress]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>ขอใบกำกับภาษีแบบเต็มรูปแบบ</CardTitle>
      </CardHeader>
      <CardContent>
        <Controller
          name="taxInvoice.wantTaxInvoice"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value ? "yes" : "no"}
              onValueChange={(value) => {
                const wantInvoice = value === "yes";
                field.onChange(wantInvoice);
              }}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="tax-yes" />
                <Label htmlFor="tax-yes">ต้องการขอใบกำกับภาษี</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="tax-no" />
                <Label htmlFor="tax-no">ไม่ต้องการขอใบกำกับภาษี</Label>
              </div>
            </RadioGroup>
          )}
        />

        {watchTaxInvoice && (
          <div className="mt-6 space-y-6 border-t pt-6">
            {/* Personal Information */}
            <div>
              <h4 className="mb-4 font-medium">ข้อมูลเบื้องต้น</h4>
              <div className="space-y-4">
                <Controller
                  name="taxInvoice.personalInfo.type"
                  control={control}
                  render={({ field }) => (
                    <Dropdown
                      id="tax-type"
                      label="ประเภท"
                      placeholder="เลือกประเภท"
                      value={field.value}
                      options={[
                        { value: "individual", label: "บุคคลธรรมดา" },
                        { value: "company", label: "นิติบุคคล" },
                      ]}
                      error={getErrorMessage(errors.taxInvoice?.personalInfo?.type)}
                      onValueChange={field.onChange}
                    />
                  )}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Controller
                    name="taxInvoice.personalInfo.firstName"
                    control={control}
                    render={({ field }) => (
                      <InputField
                        id="tax-firstName"
                        placeholder="ชื่อ"
                        error={
                          errors.taxInvoice?.personalInfo?.firstName?.message
                        }
                        {...field}
                      />
                    )}
                  />
                  <Controller
                    name="taxInvoice.personalInfo.lastName"
                    control={control}
                    render={({ field }) => (
                      <InputField
                        id="tax-lastName"
                        placeholder="นามสกุล"
                        error={
                          errors.taxInvoice?.personalInfo?.lastName?.message
                        }
                        {...field}
                      />
                    )}
                  />
                </div>

                <Controller
                  name="taxInvoice.personalInfo.email"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      id="tax-email"
                      placeholder="Email"
                      type="email"
                      error={errors.taxInvoice?.personalInfo?.email?.message}
                      {...field}
                    />
                  )}
                />

                <div>
                  <Label className="mb-2 block">เบอร์โทรศัพท์</Label>
                  <Controller
                    name="taxInvoice.personalInfo.phone"
                    control={control}
                    render={({ field }) => (
                      <PhoneInput
                        country="th"
                        value={field.value}
                        onChange={field.onChange}
                        containerClass="w-full"
                        inputClass="w-full"
                      />
                    )}
                  />
                  {errors.taxInvoice?.personalInfo?.phone && (
                    <p className="mt-1 text-xs text-red-600">
                      {errors.taxInvoice.personalInfo.phone.message}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Tax Information */}
            <div>
              <h4 className="mb-4 font-medium">ข้อมูลภาษี</h4>
              <div className="space-y-4">
                <Controller
                  name="taxInvoice.taxInfo.taxId"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      id="tax-id"
                      placeholder="หมายเลขประจำตัวผู้เสียภาษี"
                      error={errors.taxInvoice?.taxInfo?.taxId?.message}
                      {...field}
                    />
                  )}
                />

                <Controller
                  name="taxInvoice.taxInfo.useSameAddress"
                  control={control}
                  render={({ field }) => (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="same-address"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                      <Label htmlFor="same-address">
                        ใช้ที่อยู่เดียวกันกับที่อยู่จัดส่ง
                      </Label>
                    </div>
                  )}
                />

                {!watchUseSameAddress && (
                  <>
                    <Controller
                      name="taxInvoice.taxInfo.address_line1"
                      control={control}
                      render={({ field }) => (
                        <InputField
                          id="tax-address-line1"
                          placeholder="ที่อยู บรรทัดที่ 1"
                          error={
                            errors.taxInvoice?.taxInfo?.address_line1?.message
                          }
                          {...field}
                        />
                      )}
                    />

                    <Controller
                      name="taxInvoice.taxInfo.address_line2"
                      control={control}
                      render={({ field }) => (
                        <InputField
                          id="tax-address-line2"
                          placeholder="ที่อยู บรรทัดที่ 2"
                          {...field}
                        />
                      )}
                    />

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <Controller
                        name="taxInvoice.taxInfo.subdistrict"
                        control={control}
                        render={({ field }) => (
                          <InputField
                            id="tax-subdistrict"
                            placeholder="ตำบล"
                            error={
                              errors.taxInvoice?.taxInfo?.subdistrict?.message
                            }
                            {...field}
                          />
                        )}
                      />
                      <Controller
                        name="taxInvoice.taxInfo.district"
                        control={control}
                        render={({ field }) => (
                          <InputField
                            id="tax-district"
                            placeholder="อำเภอ"
                            error={
                              errors.taxInvoice?.taxInfo?.district?.message
                            }
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <Controller
                        name="taxInvoice.taxInfo.city"
                        control={control}
                        render={({ field }) => (
                          <InputField
                            id="tax-city"
                            placeholder="เมือง"
                            error={errors.taxInvoice?.taxInfo?.city?.message}
                            {...field}
                          />
                        )}
                      />
                      <Controller
                        name="taxInvoice.taxInfo.province"
                        control={control}
                        render={({ field }) => (
                          <InputField
                            id="tax-province"
                            placeholder="จังหวัด"
                            error={
                              errors.taxInvoice?.taxInfo?.province?.message
                            }
                            {...field}
                          />
                        )}
                      />
                      <Controller
                        name="taxInvoice.taxInfo.country"
                        control={control}
                        render={({ field }) => (
                          <InputField
                            id="tax-country"
                            placeholder="ประเทศ"
                            error={errors.taxInvoice?.taxInfo?.country?.message}
                            {...field}
                          />
                        )}
                      />
                      <Controller
                        name="taxInvoice.taxInfo.postal_code"
                        control={control}
                        render={({ field }) => (
                          <InputField
                            id="tax-postal-code"
                            placeholder="รหัสไปรษณีย์"
                            error={
                              errors.taxInvoice?.taxInfo?.postal_code?.message
                            }
                            {...field}
                          />
                        )}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
