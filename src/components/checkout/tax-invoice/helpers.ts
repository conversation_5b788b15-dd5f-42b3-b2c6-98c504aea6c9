import { gsap } from "gsap";
import type { UseFormWatch, UseFormSetValue } from "react-hook-form";
import type { CheckoutFormData } from "@/pages/checkout/constants";

/**
 * Helper function to safely get error message from form errors
 */
export const getErrorMessage = (error: any): string | undefined => {
  return error?.message || undefined;
};

/**
 * Copy shipping address data to tax invoice address fields
 */
export const copyShippingToTax = (
  watch: UseFormWatch<CheckoutFormData>,
  setValue: UseFormSetValue<CheckoutFormData>
) => {
  const shippingAddress = watch("shippingAddress");
  setValue("taxInvoice.taxInfo.address_line1", shippingAddress.address_line1);
  setValue("taxInvoice.taxInfo.address_line2", shippingAddress.address_line2);
  setValue("taxInvoice.taxInfo.subdistrict", shippingAddress.subdistrict);
  setValue("taxInvoice.taxInfo.district", shippingAddress.district);
  setValue("taxInvoice.taxInfo.city", shippingAddress.city);
  setValue("taxInvoice.taxInfo.province", shippingAddress.province);
  setValue("taxInvoice.taxInfo.postal_code", shippingAddress.postal_code);
  setValue("taxInvoice.taxInfo.country", shippingAddress.country);
};

/**
 * Animate card content expansion
 */
export const animateCardExpand = (element: HTMLElement) => {
  // Set initial state for animation
  gsap.set(element, {
    opacity: 0,
    y: -20,
    scale: 0.95,
  });

  // Animate to final state
  gsap.to(element, {
    opacity: 1,
    y: 0,
    scale: 1,
    duration: 0.5,
    ease: "power2.out",
  });
};

/**
 * Animate card content collapse
 */
export const animateCardCollapse = (element: HTMLElement, onComplete?: () => void) => {
  gsap.to(element, {
    opacity: 0,
    y: -20,
    scale: 0.95,
    duration: 0.3,
    ease: "power2.in",
    onComplete,
  });
};

/**
 * Handle card click with form element detection
 */
export const handleCardClick = (
  e: React.MouseEvent<HTMLDivElement>,
  onChange: (value: boolean) => void,
  value: boolean
) => {
  // Prevent card click when clicking on form elements
  const target = e.target as HTMLElement;
  if (
    target.tagName === 'INPUT' || 
    target.tagName === 'SELECT' || 
    target.tagName === 'TEXTAREA' ||
    target.tagName === 'BUTTON' ||
    target.closest('input, select, textarea, button, [role="combobox"], [role="listbox"]')
  ) {
    return;
  }
  onChange(value);
};
